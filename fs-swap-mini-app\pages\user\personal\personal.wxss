/* ==================== 样式变量（通过注释定义常用值） ==================== */
/*
  主要颜色：
  --bg-color: #f7f8fa;
  --card-bg: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --primary-color: #3671FF;
  --success-color: #10b981;
  --warning-color: #ff6b6b;

  间距：
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 20rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;

  圆角：
  --radius-sm: 12rpx;
  --radius-md: 20rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
*/

/* ==================== 全局样式 ==================== */
page {
  background: #f7f8fa;
  scrollbar-width: none; /* Firefox */
}

/* 统一隐藏滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar,
.container::-webkit-scrollbar,
view::-webkit-scrollbar,
scroll-view::-webkit-scrollbar,
.user-info::-webkit-scrollbar,
.silver-section::-webkit-scrollbar,
.balance-card::-webkit-scrollbar,
.function-card::-webkit-scrollbar,
.earn-more-section::-webkit-scrollbar,
.service-section::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

/* 容器样式 */
.container {
  background: #f7f8fa;
  min-height: 100vh;
  height: 100vh;
  width: 100%;
  padding-bottom: 40rpx;
  padding-top: 0;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* ==================== 用户信息区域 ==================== */
.user-info {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 32rpx;
  background: #fff;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.03);
  position: relative;
}

.avatar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28rpx;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.avatar-section:active {
  opacity: 0.8;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.user-avatar {
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1) !important;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.username {
  font-size: 42rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12rpx;
  letter-spacing: 0.5rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  opacity: 0.85;
}

/* 用户信息行容器 */
.user-info-rows {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 2rpx;
}

/* 每一行信息的基本样式 */
.info-row {
  display: flex;
  align-items: center;
}

/* 小区信息行 */
.residential-info {
  margin-bottom: 2rpx;
}

/* 关注和粉丝行 */
.stats-row {
  display: flex;
  align-items: center;
}

/* 关注和粉丝项 */
.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

/* 分隔线 */
.stats-divider {
  width: 1rpx;
  height: 20rpx;
  background-color: #ddd;
  margin: 0 24rpx;
}

/* 所有信息文本的通用样式 */
.info-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 6rpx;
}

/* 小区名称文本特殊处理 */
.residential-info .info-text {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
}

.signature-row {
  margin-top: 4rpx;
}

.signature-text {
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  padding-right: 28rpx;
}

.signature-text::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
}

.login-button {
  display: inline-flex;
  align-items: center;
  padding: 6rpx 20rpx;
  background: linear-gradient(90deg, #3B7FFF 0%, #2B6EF3 100%);
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(59, 127, 255, 0.25);
}

.login-button .login-arrow {
  margin-left: 6rpx;
  font-size: 24rpx;
  color: #FFFFFF;
}

.login-button:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* ==================== 功能区域样式 ==================== */
.silver-section {
  width: 100%;
  box-sizing: border-box;
  padding: 0 12rpx;
  margin-top: 24rpx;
}

/* 通用卡片样式 */
.balance-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  color: #333333;
  position: relative;
  margin-bottom: 20rpx;
}

.function-card {
  padding: 12rpx;
}

/* 卡片标题区域 */
.function-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  /* margin-bottom: 20rpx; */
}

.function-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.function-more {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 26rpx;
  color: #999999;
  padding: 12rpx 20rpx;
  margin: -12rpx -20rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.function-more:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.function-more text {
  padding: 4rpx 0;
}

/* 功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
  padding: 4rpx 0;
}

/* 5个功能项的特殊布局 */
.function-grid-five {
  grid-template-columns: repeat(5, 1fr);
  gap: 8rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 0;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12rpx;
}

.function-item-hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 图标样式 */
.function-icon {
  margin-bottom: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666;
}

/* 信息区域 */
.function-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 名称和徽章的行 */
.function-name-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rpx;
  height: 100%;
  width: 100%;
  text-align: center;
}

.function-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  display: flex;
  align-items: center;
}

/* 数字徽章样式 */
.function-badge {
  font-size: 22rpx;
  color: #333;
  margin-left: 2rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
}

/* 大号数字徽章样式 */
.function-badge-large {
  font-size: 32rpx;
  color: #666;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: -60rpx;
  right: -60rpx;
  width: 280rpx;
  height: 280rpx;
  background: radial-gradient(circle, rgba(180, 151, 90, 0.04) 0%, rgba(180, 151, 90, 0) 70%);
  border-radius: 50%;
}

.balance-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
  /* margin-top: rpx; */
  /* padding: 0 8rpx; */
}

.balance-info {
  flex: 1;
}

.coin-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 1rpx;
}

.balance-amount {
  font-size: 48rpx;
  font-weight: 600;
  background: #333;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 1.1;
  margin-bottom: 4rpx;
}

.balance-subtitle {
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0.5rpx;
}

/* ==================== 任务区域样式 ==================== */
.earn-more-section {
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  padding: 0 20rpx;
}

.earn-more-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  margin-bottom: 12rpx;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.more-task-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 26rpx;
  color: #999;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.more-task-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.more-task-btn::after {
  content: '';
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  left: -20rpx;
  z-index: -1;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 14rpx;
  padding-top: 6rpx;
}

.task-item {
  background: #f8f9fd;
  border-radius: 20rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 14rpx;
  position: relative;
  border: 2rpx solid transparent;
}

.task-item:active {
  background: #f0f2f5;
  border-color: rgba(180, 151, 90, 0.1);
}

.task-left {
  display: flex;
  align-items: center;
  gap: 14rpx;
  flex: 1;
  min-width: 0;
}

.task-icon {
  width: 68rpx;
  height: 68rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  padding: 14rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-name {
  font-size: 26rpx;
  margin-bottom: 6rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333333;
}

.task-desc {
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0.5rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.task-btn {
  background: rgba(180, 151, 90, 0.08);
  color: #B4975A;
  font-size: 24rpx;
  padding: 10rpx 28rpx;
  border-radius: 28rpx;
  border: none;
  font-weight: 600;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
  flex-shrink: 0;
  margin: 0;
}

.task-btn:active {
  opacity: 0.9;
  transform: scale(0.96);
  background: rgba(180, 151, 90, 0.12);
}

/* 任务列表容器 */
.task-list-container {
  padding: 10rpx;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
}

.task-grid-item {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.task-grid-content {
  padding: 16rpx 8rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  align-items: center;
}

.task-grid-reward {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  margin-bottom: 12rpx;
}

.task-grid-reward .reward-amount {
  font-size: 48rpx;
  color: #ff6b6b;
  font-weight: 600;
  line-height: 1;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.task-grid-reward .reward-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

.task-grid-icon {
  width: 64rpx;
  height: 64rpx;
  margin: 0 auto;
}

.task-grid-icon image {
  width: 100%;
  height: 100%;
}

.task-grid-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  width: 100%;
}

.task-grid-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 8rpx;
}

.task-grid-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  padding: 0 8rpx;
}

.task-grid-progress {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  padding: 0 12rpx;
}

.task-grid-progress .progress-text {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

.task-grid-progress .progress-bar {
  height: 4rpx;
  background: #f5f5f5;
  border-radius: 2rpx;
  overflow: hidden;
}

.task-grid-progress .progress-fill {
  height: 100%;
  background: #3671FF;
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.task-grid-action {
  width: 100%;
  margin-top: 8rpx;
  padding: 0 12rpx;
}

.task-grid-action .action-btn {
  width: 100%;
  height: 48rpx;
  line-height: 48rpx;
  font-size: 24rpx;
  padding: 0;
  margin: 0;
  border-radius: 24rpx;
  background: #3671FF;
  color: #fff;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.task-grid-action .action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.task-grid-action .action-btn.completed-btn {
  background: #f5f5f5;
  color: #999;
}

.task-grid-action .action-btn.claim-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4rpx 14rpx rgba(16, 185, 129, 0.35);
}

.task-grid-action .action-btn.claim-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}

/* 空状态 */
.empty-task-state {
  text-align: center;
  padding: 80rpx 20rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.empty-task-state .empty-text {
  font-size: 26rpx;
  color: #9ca3af;
  margin-top: 20rpx;
  font-weight: 500;
}

/* 快捷任务列表样式 */
.quick-task-list {
  padding: 16rpx 4rpx;
}

.quick-task-list .task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.quick-task-list .task-item:last-child {
  margin-bottom: 0;
}

.quick-task-list .task-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quick-task-list .task-icon {
  width: 80rpx;
  height: 80rpx;
}

.quick-task-list .task-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.quick-task-list .task-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.quick-task-list .task-desc {
  font-size: 24rpx;
  color: #666;
}

.quick-task-list .task-btn {
  padding: 0 32rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-size: 26rpx;
  color: #3671FF;
  background: rgba(54, 113, 255, 0.1);
  border-radius: 28rpx;
  border: none;
}

.quick-task-list .task-btn.button-hover {
  opacity: 0.8;
}

/* 操作按钮 */
.task-action {
  flex-shrink: 0;
}

.action-btn {
  min-width: 100rpx;
  height: 48rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}

.claim-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4rpx 14rpx rgba(16, 185, 129, 0.35);
}

.claim-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}

.completed-btn {
  background: #f8fafc;
  color: #9ca3af;
  border: 1rpx solid #e5e7eb;
}

.progress-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4rpx 14rpx rgba(59, 130, 246, 0.35);
}

.progress-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
}

.view-more-tasks {
  margin-top: 20rpx;
  padding: 20rpx;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  background: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* ==================== 弹窗样式 ==================== */
.slogan-popup-content {
  position: relative;
  padding: 0 32rpx;
}

.slogan-popup-close {
  position: absolute;
  right: 32rpx;
  top: 0;
  padding: 16rpx;
  z-index: 1;
}

.slogan-popup-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.slogan-popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.slogan-popup-subtitle {
  font-size: 26rpx;
  color: #999;
  display: block;
}

.slogan-popup-input {
  margin-bottom: 24rpx;
}

.slogan-input {
  font-size: 28rpx !important;
  color: #333 !important;
}

.silver-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 32rpx;
  padding: 0 24rpx;
}

.silver-icon {
  width: 32rpx;
  height: 32rpx;
  background: url('https://file.cpbara.com/jury/system/total-coin.png') no-repeat center;
  background-size: contain;
}

.silver-text {
  font-size: 26rpx;
  color: #666;
}

.slogan-popup-btn {
  padding: 0 24rpx;
}

.slogan-popup-btn .confirm-btn {
  height: 88rpx !important;
  line-height: 88rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  background: #3671FF !important;
  border: none !important;
}

/* ==================== 客服与反馈区域 ==================== */
.service-section {
  width: 100%;
  box-sizing: border-box;
  padding: 0 12rpx;
  margin-bottom: 24rpx;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  padding: 4rpx 0;
}

.service-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: #fff;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}

.service-button:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.service-icon {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.service-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* ==================== 响应式和兼容性 ==================== */
/* 全局禁用水平滚动 */
page, view, scroll-view, text, image, button {
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

/* 确保所有容器都不会超出屏幕宽度 */
.user-info,
.silver-section,
.balance-card,
.function-card,
.earn-more-section,
.service-section {
  max-width: 100vw !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}



